package com.snct.service;

import com.alibaba.fastjson.JSONObject;
import com.snct.common.core.domain.entity.SysDept;
import com.snct.dctcore.commoncore.constants.RedisParameter;
import com.snct.dctcore.commoncore.domain.hbase.*;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.dctcore.hbasecore.utils.HBaseDaoUtil;
import com.snct.service.device.AttitudeService;
import com.snct.service.device.AwsService;
import com.snct.service.device.GpsService;
import com.snct.system.domain.BuDeviceConfig;
import com.snct.system.domain.BuShipWarning;
import com.snct.system.domain.Device;
import com.snct.system.domain.Ship;
import com.snct.system.domain.dto.ShipSimpleDto;
import com.snct.system.service.IBuDeviceConfigService;
import com.snct.system.service.IBuShipWarningService;
import com.snct.system.service.IShipService;
import com.snct.system.service.ISysDeptService;
import com.snct.system.service.impl.DeviceServiceImpl;
import com.snct.typhoon.service.TyphoonService;
import com.snct.typhoon.service.TyphoonWarningService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.text.DecimalFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * <AUTHOR>
 * aws 操作类
 */
@Service
@Transactional(readOnly = true)
public class RealtimeService {
    private Logger logger = LoggerFactory.getLogger(RealtimeService.class);

    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private DeviceServiceImpl deviceService;
    @Autowired
    private AwsService awsService;
    @Autowired
    private AttitudeService attitudeService;
    @Autowired
    private GpsService gpsService;
    @Autowired
    private ISysDeptService deptService;
    @Autowired
    private IShipService shipService;
    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;
    @Autowired
    private TyphoonWarningService typhoonWarningService;
    @Autowired
    private IBuShipWarningService shipWarningService;
    @Autowired
    private TyphoonService typhoonService;
    @Autowired
    private IBuDeviceConfigService buDeviceConfigService;

    /**
     * 获取数据公用方法
     */
    public String getReidsNewData(String sn, String deviceCode) {
        String jsonString = null;
        ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
        if (redisTemplate.hasKey(RedisParameter.DEVICE_DATA + sn + ":" + deviceCode)) {
            jsonString = valueOperations.get(RedisParameter.DEVICE_DATA + sn + ":" + deviceCode);
        }
        return jsonString;
    }


    public Object getLatestData(String sn, Long deviceType) {
        // 根据 sn 和设备类型查询设备列表
        List<Device> devices = deviceService.selectSimpleDeviceListBySnAndType(sn, deviceType);

        // 检查设备列表是否为空
        if (devices == null || devices.isEmpty()) {
            return null;
        }

        // 按优先级排序，取优先级最高的设备（cost 值最大的）
        Device selectedDevice = devices.stream()
                .filter(device -> device.getCost() != null)
                .max(Comparator.comparing(Device::getCost))
                .orElse(devices.get(0));

        // 使用选中设备的 deviceCode 调用原有方法查询数据
        return getLatestData(sn, selectedDevice.getCode());
    }

    public Object getLatestData(String sn, String deviceCode) {
        //String str = getReidsNewData(sn, deviceCode);
        //
        //if (StringUtils.isNotBlank(str) && !"null".equals(str)) {
        //    return JSONObject.parseObject(str);
        //}

        Device device = deviceService.selectDeviceBySnAndCode(sn, deviceCode);
        if (device == null) {
            return null;
        }
        Object object = null;

        if (DeviceTypeEnum.GPS.getValue().equals(device.getType())) {
            object = gpsService.getLatestDataFromHbase(sn, deviceCode);
        }
        if (DeviceTypeEnum.AWS.getValue().equals(device.getType())) {
            object = awsService.getLatestDataFromHbase(sn, deviceCode);
        }
        if (DeviceTypeEnum.ATTITUDE.getValue().equals(device.getType())) {
            object = attitudeService.getLatestDataFromHbase(sn, deviceCode);
        }

        ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
        valueOperations.set(RedisParameter.DEVICE_DATA + sn + ":" + deviceCode, JSONObject.toJSONString(object), 3, TimeUnit.MINUTES);

        return object;
    }


    /**
     * zhoufd
     * 数据包代码说明：
     * 0A01 入驻企业列表
     * 0A02 入驻企业/船只数量汇总
     * 0A03 天气信息/预警信息（暂定）
     * 0A04 地图数据-企业名称与坐标列表
     * 0B01 船基本信息
     * 0B02 姿态信息
     * 0B03 气象信息信息
     * 0B04 天气信息/预警信息（暂定）
     * 0B05 企业总接入船只数量
     * 0B06 地图数据-船只名称与坐标列表
     * 0C01 视频监控实时播放地址列表
     * 0C02 视频监控历史记录列表  (废弃，通过接口)
     * 0D01 船基本信息  （与  0B01可以通用）
     * 0D02 历史轨迹节点信息
     * 0E01 PDU信息
     * 0E02 卫星猫信息
     * 0E03 功放信息
     * 0F01 根据船舶SN查询设备列表
     * 0F02 根据船舶SN和设备类型查询设备列表
     */
    public Object getSrceenLatestData(String deptId,String sn, String dataCode) {
        return getSrceenLatestData(deptId, sn, dataCode, null);
    }

    /**
     * 获取屏幕最新数据（支持设备类型参数）
     * @param deptId 部门ID
     * @param sn 船舶SN
     * @param dataCode 数据代码
     * @param deviceType 设备类型（可选）
     * @return 数据对象
     */
    public Object getSrceenLatestData(String deptId,String sn, String dataCode, Integer deviceType) {

        Object object = null;

        if("0A01".equals(dataCode)){
            return getDeptList();             //入驻企业列表
        }
        if("0A02".equals(dataCode)){
            return getDeptStatistics();       //入驻企业与船只汇总
        }
        if("0A03".equals(dataCode)){
            return getWarningInfo();          //预警/天气
        }
        if("0A04".equals(dataCode)){
            return getShipListDetail(deptId);   //船只列表 带经纬度
        }
        //if("0A04".equals(dataCode)){
        //    return getDeptDetailList();       //入驻企业列表 带经纬度
        //}

        if("0B01".equals(dataCode)){
            return getShipDetail(sn);         //船只信息
        }
        if("0B02".equals(dataCode)){
            return getAttitudeDetail(sn);      //姿态仪最新数据信息
        }
        if("0B03".equals(dataCode)){
            return getAwsDetail(sn);          //气象仪最新数据信息
        }
        if("0B04".equals(dataCode)){
            return getShipWarningInfo(sn);     //船舶预警信息
        }
        if("0B05".equals(dataCode)){
            return getShipStatistics(deptId);  //企业总接入船只数量
        }
        if("0B06".equals(dataCode)){
            return getShipListDetail(deptId);   //地图数据-船只名称与坐标列表
        }
        if("0C01".equals(dataCode)){
            return object = new HashMap<>();   //视频监控实时播放地址列表
        }
        if("0C02".equals(dataCode)){
            return object = new HashMap<>();   //视频监控历史记录列表
        }
        if("0D01".equals(dataCode)){
            return getShipDetail(sn);          //船基本信息  （与  0B01可以通用）
        }
        if("0D02".equals(dataCode)){
            return getShipGpsList(sn);         //历史轨迹节点信息
        }
        if("0E01".equals(dataCode)){
            return getPduInfoList(sn);         //PDU信息
        }
        if("0E02".equals(dataCode)){
            return getModemInfoList(sn);       //卫星猫信息
        }
        if("0E03".equals(dataCode)){
            return getAmplifierList(sn);       //功放信息
        }
        return object;
    }

    //读取企业列表
    private Object getDeptList(){
        List<SysDept> dlist = deptService.selectDeptByPid(0l);
        List<Map> relist = new ArrayList<>();;
        if(dlist.size()==1){
            SysDept sysDept =  dlist.get(0);
            List<SysDept>  list = deptService.selectDeptByPid(sysDept.getDeptId());
            for(SysDept dept : list){
                Map map = new HashMap();
                map.put("deptId", dept.getDeptId());
                map.put("deptName", dept.getDeptName());
                map.put("status", dept.getStatus());
                relist.add(map);
            }
        }
        return relist;
    }
    //读取企业列表带经纬度
    private Object getDeptDetailList(){
        List<SysDept> dlist = deptService.selectDeptByPid(0l);
        List<Map> relist = new ArrayList<>();
        if(dlist.size()==1){
            SysDept sysDept =  dlist.get(0);
            List<SysDept>  list = deptService.selectDeptDetailByPid(sysDept.getDeptId());
            for(SysDept dept : list){
                Map map = new HashMap();
                map.put("deptId", dept.getDeptId());
                map.put("deptName", dept.getDeptName());
                map.put("status", dept.getStatus());
                map.put("latitude", dept.getLatitude());
                map.put("longitude", dept.getLongitude());
                relist.add(map);
            }
        }
        return relist;
    }

    //入驻企业汇总，船只汇总 统计
    private Object getDeptStatistics(){
        List<SysDept> dlist = deptService.selectDeptByPid(0l);
        Map<String, Integer> remap = new HashMap<>();
        if(dlist.size()==1){
            SysDept sysDept =  dlist.get(0);
            remap.put("enterprise_num",deptService.selectDeptByPid(sysDept.getDeptId()).size());
            remap.put("ship_num",shipService.selectShipListCount(new Ship()));
        }else{
            remap.put("enterprise_num",0);
            remap.put("ship_num",0);
        }
        return remap;
    }

    //预警信息 - 0A03数据包：获取所有船舶的预警信息和台风信息
    private Object getWarningInfo(){
        Map<String, Object> result = new HashMap<>();

        BuShipWarning buShipWarning = new BuShipWarning();
        List<BuShipWarning> warningList = shipWarningService.selectBuShipWarningListWithLimit(buShipWarning, 5);
        result.put("warningInfo", warningList);

        Map<String, Object> typhoonData = typhoonService.getActiveTyphoonSummary();
        if (typhoonData != null) {
            typhoonData.remove("@type");
        }
        result.put("typhoonInfo", typhoonData);

        return result;
    }

    //船舶预警信息 - 0B04数据包：获取单条船的预警信息
    private Object getShipWarningInfo(String sn) {
        Map<String, Object> result = new HashMap<>();

        BuShipWarning buShipWarning = new BuShipWarning();
        buShipWarning.setSn(sn);
        List<BuShipWarning> warningList = shipWarningService.selectBuShipWarningListWithLimit(buShipWarning, 5);
        result.put("warningInfo", warningList);

        Map<String, Object> typhoonData = typhoonService.getActiveTyphoonSummary();
        if (typhoonData != null) {
            typhoonData.remove("@type");
        }
        result.put("typhoonInfo", typhoonData);

        return result;
    }

    //船的基本信息
    private Object getShipDetail(String sn){

        Ship ship = shipService.selectShipByShipSn(sn);
        if(ship!=null){
            Map<String, Object> map = new HashMap<>();
            map.put("name",ship.getName());
            map.put("mmsi", ship.getMmsi());
            map.put("callSign", ship.getCallSign());
            map.put("sn", sn);
            GpsHbaseVo latestData = (GpsHbaseVo) getLatestData(sn, "032A");
            map.put("longitude", latestData==null?"0.000000" : latestData.getLongitude());
            map.put("latitude",latestData==null?"0.000000" : latestData.getLatitude());
            map.put("latitudeHemisphere",latestData==null?"0" : latestData.getLatitudeHemisphere());
            map.put("longitudeHemisphere",latestData==null?"0" : latestData.getLongitudeHemisphere());
            map.put("utc",latestData==null?"N/A" : latestData.getUtcTime());
            return map;
        }
        return new HashMap<String, Object>();
    }

    private Object getAttitudeDetail(String sn){
        //获取姿态信息
        AttitudeHbaseVo latestData = (AttitudeHbaseVo) getLatestData(sn, Long.valueOf(DeviceTypeEnum.ATTITUDE.getValue()));
        Map<String, Object> map = new HashMap<>();
        map.put("attitudeHeading",latestData==null?"0" : latestData.getHeading());   //船首向
        map.put("attitudeRolling", latestData==null?"0" : latestData.getRolling());
        map.put("attitudePitch", latestData==null?"0" : latestData.getPitch());
        map.put("attitudeLongitude",latestData==null?"0.000000" : latestData.getLon());
        map.put("attitudeLatitude",latestData==null?"0.000000" : latestData.getLat());
        map.put("attitudeHeight",latestData==null?"0" : latestData.getHeight());
        map.put("attitudeSpeed", latestData==null?"0" : latestData.getSpeed());

        Device device = deviceService.selectDeviceBySnAndCode(sn,"033A");

        map.put("attitudeDistance", latestData==null?"0" : countDistance(latestData.getLon(),latestData.getLat(),device.getId()));
        map.put("attitudeUptime", latestData==null?"N/A" : latestData.getInitialBjTime());    //数据更新时间
        map.put("attitudeSn", sn);
        return map;
    }

    private Object getAwsDetail(String sn){
        //获取气象信息
        AwsHbaseVo latestData = (AwsHbaseVo) getLatestData(sn, Long.valueOf(DeviceTypeEnum.AWS.getValue()));
        Map<String, Object> map = new HashMap<>();
        map.put("awsSpeed", latestData==null?"0" : latestData.getRelativeWindSpeed());
        map.put("awsDirection", latestData==null?"0" : latestData.getRelativeWind());
        map.put("awsUptime", latestData==null?"N/A" : latestData.getInitialBjTime());
        map.put("awsSn", sn);
        return map;
    }

    private Object getShipStatistics(String deptId){
        List<ShipSimpleDto> list = shipService.selectSimpleShipListByDeptId(Long.parseLong(deptId));
        Map<String, Integer> remap = new HashMap<>();
        remap.put("shipNum",list.size());
        return remap;
    }

    private Object getShipListDetail(String deptId){
        List<ShipSimpleDto> list = shipService.selectSimpleShipListByDeptId(Long.parseLong(deptId));
        List<Map> relist = new ArrayList<>();
        for(ShipSimpleDto shipSimpleDto : list){
            Map<String, String> remap = new HashMap<>();
            remap.put("name",shipSimpleDto.getName());
            remap.put("sn",shipSimpleDto.getSn());
            remap.put("shipId",shipSimpleDto.getShipId()+"");
            GpsHbaseVo latestData = (GpsHbaseVo) getLatestData(shipSimpleDto.getSn(), Long.valueOf(DeviceTypeEnum.GPS.getValue()));
            remap.put("longitude",latestData==null?"0" : latestData.getLongitude());
            remap.put("latitude",latestData==null?"0" : latestData.getLatitude());
            remap.put("latitudeHemisphere",latestData==null?"0" : latestData.getLatitudeHemisphere());
            remap.put("longitudeHemisphere",latestData==null?"0" : latestData.getLongitudeHemisphere());
            remap.put("utc",latestData==null?"N/A" : latestData.getUtcTime());
            relist.add(remap);
        }
        return relist;
    }

    private Object getShipGpsList(String sn){
        return new HashMap<String, Object>();
    }
    private Object getPduInfoList(String sn){
        //获取PDU信息
        PduHbaseVo latestData = (PduHbaseVo) getLatestData(sn, Long.valueOf(DeviceTypeEnum.PDU.getValue()));
        Map<String, Object> map = new HashMap<>();

        if (latestData != null) {
            // 基础电力参数
            map.put("recordTime", latestData.getInitialBjTime()); // 记录时间
            map.put("totalEnergy", latestData.getManage()); // 总电能
            map.put("totalCurrent", latestData.getElectric()); // 总电流
            map.put("voltage", latestData.getVoltage()); // 电压
            map.put("activePower", latestData.getYesPower()); // 有功功率
            map.put("reactivePower", latestData.getNoPower()); // 无功功率
            map.put("apparentPower", latestData.getSeePower()); // 视在功率
            map.put("powerFactor", latestData.getPowerParam()); // 功率因数

            // 通道信息列表
            List<Map<String, Object>> channels = new ArrayList<>();
            for (int i = 1; i <= 8; i++) {
                Map<String, Object> channel = new HashMap<>();
                channel.put("channelIndex", i);
                channel.put("current", latestData.getChannelElectric(i));
                channel.put("power", latestData.getChannelPower(i));
                channel.put("status", latestData.getChannelStatus(i));
                channels.add(channel);
            }
            map.put("channels", channels);

            // 数据状态
            map.put("dataValid", true);
        } else {
            map.put("dataValid", false);
            map.put("recordTime", null);
        }

        return map;
    }
    private Object getModemInfoList(String sn){
        return new HashMap<String, Object>();
    }
    private Object getAmplifierList(String sn){
        return new HashMap<String, Object>();
    }

    /**
     * 根据船舶sn和设备类型查询设备列表
     * @param sn 船舶sn
     * @param deviceType 设备类型，为null时查询所有类型
     * @return 设备列表
     */
    public List<Device> getDeviceListBySnAndType(String sn, Integer deviceType) {
        if (sn == null || sn.trim().isEmpty()) {
            logger.warn("船舶SN为空，无法查询设备列表");
            return new ArrayList<>();
        }

        try {
            // 清理sn，去除可能的"SN"前缀
            String cleanSn = sn.trim().replace("SN", "");

            // 构建缓存key，包含设备类型信息
            String cacheKey = "DEVICE_LIST_" + cleanSn + (deviceType != null ? "_TYPE_" + deviceType : "_ALL");

            // 先尝试从Redis缓存获取
            ValueOperations<String, List<Device>> valueOperations = redisTemplate.opsForValue();
            List<Device> cachedDeviceList = valueOperations.get(cacheKey);

            if (cachedDeviceList != null && !cachedDeviceList.isEmpty()) {
                return cachedDeviceList;
            }

            // 缓存中没有，从数据库查询
            List<Device> deviceList = null;
            if (deviceType != null) {
                // 根据sn和设备类型查询
                deviceList = deviceService.selectSimpleDeviceListBySnAndType(cleanSn, deviceType.longValue());
            } else {
                return deviceList;
            }

            if (deviceList != null && !deviceList.isEmpty()) {
                // 将结果缓存5分钟
                valueOperations.set(cacheKey, deviceList, 5, TimeUnit.MINUTES);
            } else {
                deviceList = new ArrayList<>();
            }
            return deviceList;

        } catch (Exception e) {
            return new ArrayList<>();
        }
    }


    /**
     * 谷歌地图计算两个坐标点的距离
     * @param lon  经度
     * @param lat  纬度
     * @return 距离（米）
     */
    private static double EARTH_RADIUS = 6378.137;  //地球半径
    private String countDistance(String lon, String lat,Long deviceId){
        if( (lon+"").length()<5 || (lat+"").length()<5){
            return "0米";
        }
        //初始坐标 深圳经度114.114449纬度:22.543413
        String initial_lon = "114.114449";
        String initial_lat = "22.543413";
        BuDeviceConfig buDeviceConfig = new BuDeviceConfig();
        buDeviceConfig.setDeviceId(deviceId);
        buDeviceConfig.setConfigKey("init_lng");
        BuDeviceConfig config_init_lng = buDeviceConfigService.selectBydeviceIdAndKey(buDeviceConfig);
        buDeviceConfig.setConfigKey("init_lat");
        BuDeviceConfig config_init_lat = buDeviceConfigService.selectBydeviceIdAndKey(buDeviceConfig);
        if(config_init_lng!=null&&config_init_lat!=null){
            initial_lon = config_init_lng.getConfigValue();
            initial_lat = config_init_lat.getConfigValue();
        }
        double radLat1 = Math.toRadians(Double.parseDouble(initial_lat));
        double radLat2 = Math.toRadians(Double.parseDouble(lat));
        double a = radLat1 - radLat2;
        double b = Math.toRadians(Double.parseDouble(initial_lon)) - Math.toRadians(Double.parseDouble(lon));
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a/2),2) +
                Math.cos(radLat1)*Math.cos(radLat2)*Math.pow(Math.sin(b/2),2)));
        s = s * EARTH_RADIUS;
        s = Math.round(s * 10000) / 10;
        String res = String.valueOf(s)+"米";
        if(s>=1000){
            s = s/1000;
            DecimalFormat df = new DecimalFormat("0.00");
            res = df.format(s)+"千米";
        }
        return res;
    }


}

package com.snct.service.device;

import com.snct.dctcore.commoncore.domain.hbase.AttitudeHbaseVo;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: AttitudeService
 * @Description: 姿态数据服务类
 * @author: wzewei
 * @date: 2025-07-30 14:53
 */
@Service
@Transactional(readOnly = true)
public class AttitudeService {

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    private final Logger logger = LoggerFactory.getLogger(AttitudeService.class);
    /**
     * 根据时间范围来查询数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<AttitudeHbaseVo> queryByTime(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {

        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.ATTITUDE.getAlias(), deviceCode, interval);
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new AttitudeHbaseVo(), tableName, rowList);
    }

    public AttitudeHbaseVo getLatestDataFromHbase(String sn, String deviceCode) {
        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.ATTITUDE.getAlias(), deviceCode,100);
        return hBaseDaoUtil.getLatestRow(new AttitudeHbaseVo(), tableName);
    }

}

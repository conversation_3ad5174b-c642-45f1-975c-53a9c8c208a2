package com.snct.service.device;

import com.snct.dctcore.commoncore.domain.hbase.AwsHbaseVo;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: AwsService
 * @Description: 气象站数据服务
 * @author: wzewei
 * @date: 2025-08-15 09:21
 */
@Service
@Transactional(readOnly = true)
public class AwsService {

    private final Logger logger = LoggerFactory.getLogger(AwsService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;


    /**
     * 根据时间范围来查询数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<AwsHbaseVo> queryByTime(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {

        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.AWS.getAlias(), deviceCode, interval);
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new AwsHbaseVo(), tableName, rowList);
    }

    public AwsHbaseVo getLatestDataFromHbase(String sn, String deviceCode) {
        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.AWS.getAlias(), deviceCode,100);
        return hBaseDaoUtil.getLatestRow(new AwsHbaseVo(), tableName);
    }

}

package com.snct.service.device;

import com.snct.dctcore.commoncore.domain.hbase.PduHbaseVo;
import com.snct.dctcore.commoncore.enums.DeviceTypeEnum;
import com.snct.dctcore.hbasecore.utils.HBaseDaoUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * @ClassName: PduService
 * @Description: Pdu数据服务
 * @author: wzewei
 * @date: 2025-08-15 09:21
 */
@Service
@Transactional(readOnly = true)
public class PduService {

    private final Logger logger = LoggerFactory.getLogger(PduService.class);

    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;


    /**
     * 根据时间范围来查询数据
     *
     * @param startTime
     * @param endTime
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<PduHbaseVo> queryByTime(String sn, String deviceCode, Integer interval, Long startTime, Long endTime) {

        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.PDU.getAlias(), deviceCode, interval);
        List<Map<String, String>> rowList = hBaseDaoUtil.getRowListByTime(startTime, endTime);
        return hBaseDaoUtil.scanByRowList(new PduHbaseVo(), tableName, rowList);
    }

    public PduHbaseVo getLatestDataFromHbase(String sn, String deviceCode) {
        String tableName = hBaseDaoUtil.getTableName(sn, DeviceTypeEnum.PDU.getAlias(), deviceCode,100);
        return hBaseDaoUtil.getLatestRow(new PduHbaseVo(), tableName);
    }

}

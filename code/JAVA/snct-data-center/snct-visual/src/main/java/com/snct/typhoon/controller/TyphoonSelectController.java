package com.snct.typhoon.controller;

import com.alibaba.fastjson.JSONObject;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.typhoon.domain.Typhoon;
import com.snct.typhoon.domain.vo.TyphoonInfoVo;
import com.snct.typhoon.domain.vo.TyphoonVo;
import com.snct.typhoon.service.SelectTyService;
import com.snct.typhoon.service.TyphoonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: TyphoonSelectController
 * @Description: 台风查询
 * @author: wzewei
 * @date: 2025-08-19 16:39:16
 */
@CrossOrigin
@RestController
@RequestMapping("/api/typhoon")
public class TyphoonSelectController extends BaseController {
    @Autowired
    private SelectTyService selectTyService;

    @Autowired
    private TyphoonService typhoonService;

    @GetMapping("/syncHis")
    public void syncHis(Integer year) {
        typhoonService.obtainTyphoon(year);
    }

    @GetMapping("/getActive")
    public AjaxResult getActiveTyphoon() {
        try {
            typhoonService.obtainTyphoon(null);
            Map<String, Object> data = typhoonService.getActiveTyphoonSummary();
            return AjaxResult.success(data);
        } catch (Exception e) {
            return AjaxResult.error("获取活跃台风摘要信息失败");
        }
    }

    @GetMapping("/getTyphoonData")
    public AjaxResult getTyphoonData(Integer yearsTime) {
        typhoonService.obtainTyphoon(yearsTime);
        List<Typhoon> list = selectTyService.selectTyphoonList(yearsTime);
        if(list==null){
            list = new ArrayList<Typhoon>();
            return AjaxResult.success(list);
        }
        List<TyphoonVo> typhoonList = JSONObject.parseArray(list.toString(), TyphoonVo.class);
        return AjaxResult.success(typhoonList);
    }

    @GetMapping("/getTyphoonPoint")
    public AjaxResult getTyphoonPoint(Integer tfId) {
        typhoonService.obtainTyphoon(null);
        List<TyphoonInfoVo> list = selectTyService.selectTyphoonPoint(tfId);
        if(list==null){
            list = new ArrayList<TyphoonInfoVo>();
            return AjaxResult.success(list);
        }
        List<TyphoonInfoVo> typhoonInfoVoList = JSONObject.parseArray(list.toString(), TyphoonInfoVo.class);
        return AjaxResult.success(typhoonInfoVoList);
    }

}
